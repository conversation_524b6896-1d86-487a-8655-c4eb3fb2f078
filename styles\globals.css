@tailwind base;
@tailwind components;
@tailwind utilities;

body {
  font-family: Arial, Helvetica, sans-serif;
}

@layer utilities {
  .text-balance {
    text-wrap: balance;
  }
}

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 0 0% 3.9%;
    --card: 0 0% 100%;
    --card-foreground: 0 0% 3.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 0 0% 3.9%;
    --primary: 0 0% 9%;
    --primary-foreground: 0 0% 98%;
    --secondary: 0 0% 96.1%;
    --secondary-foreground: 0 0% 9%;
    --muted: 0 0% 96.1%;
    --muted-foreground: 0 0% 45.1%;
    --accent: 0 0% 96.1%;
    --accent-foreground: 0 0% 9%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;
    --border: 0 0% 89.8%;
    --input: 0 0% 89.8%;
    --ring: 0 0% 3.9%;
    --chart-1: 12 76% 61%;
    --chart-2: 173 58% 39%;
    --chart-3: 197 37% 24%;
    --chart-4: 43 74% 66%;
    --chart-5: 27 87% 67%;
    --radius: 0.5rem;
    --sidebar-background: 0 0% 98%;
    --sidebar-foreground: 240 5.3% 26.1%;
    --sidebar-primary: 240 5.9% 10%;
    --sidebar-primary-foreground: 0 0% 98%;
    --sidebar-accent: 240 4.8% 95.9%;
    --sidebar-accent-foreground: 240 5.9% 10%;
    --sidebar-border: 220 13% 91%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
  .dark {
    --background: 0 0% 3.9%;
    --foreground: 0 0% 98%;
    --card: 0 0% 3.9%;
    --card-foreground: 0 0% 98%;
    --popover: 0 0% 3.9%;
    --popover-foreground: 0 0% 98%;
    --primary: 0 0% 98%;
    --primary-foreground: 0 0% 9%;
    --secondary: 0 0% 14.9%;
    --secondary-foreground: 0 0% 98%;
    --muted: 0 0% 14.9%;
    --muted-foreground: 0 0% 63.9%;
    --accent: 0 0% 14.9%;
    --accent-foreground: 0 0% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;
    --border: 0 0% 14.9%;
    --input: 0 0% 14.9%;
    --ring: 0 0% 83.1%;
    --chart-1: 220 70% 50%;
    --chart-2: 160 60% 45%;
    --chart-3: 30 80% 55%;
    --chart-4: 280 65% 60%;
    --chart-5: 340 75% 55%;
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}

/* Hero Text Animation */
@keyframes slideInUp {
  0% {
    opacity: 0;
    transform: translateY(150px) scale(0.7) rotateX(90deg);
  }
  30% {
    opacity: 0.8;
    transform: translateY(20px) scale(0.95) rotateX(20deg);
  }
  60% {
    opacity: 1;
    transform: translateY(-5px) scale(1.05) rotateX(-5deg);
  }
  100% {
    opacity: 1;
    transform: translateY(0) scale(1) rotateX(0deg);
  }
}

@keyframes slideOutUp {
  0% {
    opacity: 1;
    transform: translateY(0) scale(1) rotateX(0deg);
  }
  40% {
    opacity: 0.8;
    transform: translateY(-20px) scale(1.05) rotateX(-20deg);
  }
  100% {
    opacity: 0;
    transform: translateY(-150px) scale(0.7) rotateX(-90deg);
  }
}

@keyframes glow {
  0%, 100% {
    text-shadow: 0 0 20px rgba(251, 191, 36, 0.5), 0 0 40px rgba(251, 191, 36, 0.3);
  }
  50% {
    text-shadow: 0 0 30px rgba(251, 191, 36, 0.8), 0 0 50px rgba(251, 191, 36, 0.6), 0 0 70px rgba(251, 191, 36, 0.4);
  }
}

.animate-slide-in {
  animation: slideInUp 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94), glow 3s ease-in-out infinite;
}

.animate-slide-out {
  animation: slideOutUp 0.5s cubic-bezier(0.55, 0.06, 0.68, 0.19);
}

.perspective-1000 {
  perspective: 1000px;
}

/* Jump Bounce effect - like bouncing ball with realistic physics */
@keyframes jumpBounce {
  0% {
    opacity: 0;
    transform: translateY(200px) scale(0.3) rotateX(90deg);
    filter: blur(2px);
  }
  15% {
    opacity: 0.5;
    transform: translateY(50px) scale(0.8) rotateX(45deg);
    filter: blur(1px);
  }
  30% {
    opacity: 0.8;
    transform: translateY(-40px) scale(1.3) rotateX(-20deg);
    filter: blur(0px);
  }
  45% {
    opacity: 1;
    transform: translateY(15px) scale(0.85) rotateX(10deg);
  }
  60% {
    transform: translateY(-20px) scale(1.1) rotateX(-10deg);
  }
  75% {
    transform: translateY(8px) scale(0.95) rotateX(5deg);
  }
  85% {
    transform: translateY(-8px) scale(1.03) rotateX(-3deg);
  }
  95% {
    transform: translateY(2px) scale(0.99) rotateX(1deg);
  }
  100% {
    opacity: 1;
    transform: translateY(0) scale(1) rotateX(0deg);
    filter: blur(0px);
  }
}

/* Ground impact effect with ripples */
@keyframes groundImpact {
  0% {
    box-shadow: 0 0 0 rgba(251, 191, 36, 0);
  }
  30% {
    box-shadow: 0 30px 60px rgba(251, 191, 36, 0.4),
                0 15px 30px rgba(251, 191, 36, 0.3),
                0 5px 15px rgba(251, 191, 36, 0.2);
  }
  45% {
    box-shadow: 0 20px 40px rgba(251, 191, 36, 0.3),
                0 10px 20px rgba(251, 191, 36, 0.2);
  }
  60% {
    box-shadow: 0 15px 30px rgba(251, 191, 36, 0.2),
                0 5px 15px rgba(251, 191, 36, 0.1);
  }
  85% {
    box-shadow: 0 8px 16px rgba(251, 191, 36, 0.1);
  }
  100% {
    box-shadow: 0 0 0 rgba(251, 191, 36, 0);
  }
}

/* Shake effect for impact */
@keyframes shake {
  0%, 100% { transform: translateX(0); }
  30% { transform: translateX(-2px); }
  45% { transform: translateX(2px); }
  60% { transform: translateX(-1px); }
  75% { transform: translateX(1px); }
}

.animate-jump-bounce {
  animation: jumpBounce 1.2s cubic-bezier(0.25, 0.46, 0.45, 0.94),
             glow 3s ease-in-out infinite,
             groundImpact 1.2s ease-out,
             shake 0.3s ease-in-out 0.45s;
}
