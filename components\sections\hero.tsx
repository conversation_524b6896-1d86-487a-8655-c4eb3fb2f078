"use client"

import { useEffect, useRef, useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import {
  ArrowRight,
  Home,
  Key,
  Building2,
  Handshake,
  TrendingUp,
  FileText,
  Users,
  Award,
  Phone,
  MessageCircle,
} from "lucide-react"
import Link from "next/link"

const services = [
  {
    icon: Home,
    title: "Buy Property",
    description: "Find your dream home with expert guidance",
    color: "from-blue-500 to-blue-600",
    bgColor: "bg-blue-50",
    iconColor: "text-blue-600",
  },
  {
    icon: Key,
    title: "Rent Property",
    description: "Discover perfect rental properties",
    color: "from-green-500 to-green-600",
    bgColor: "bg-green-50",
    iconColor: "text-green-600",
  },
  {
    icon: Building2,
    title: "Commercial Lease",
    description: "Premium commercial spaces for business",
    color: "from-purple-500 to-purple-600",
    bgColor: "bg-purple-50",
    iconColor: "text-purple-600",
  },
  {
    icon: FileText,
    title: "Pre-lease",
    description: "Early access to upcoming projects",
    color: "from-orange-500 to-orange-600",
    bgColor: "bg-orange-50",
    iconColor: "text-orange-600",
  },
  {
    icon: Handshake,
    title: "Sell Property",
    description: "Maximize your property value",
    color: "from-red-500 to-red-600",
    bgColor: "bg-red-50",
    iconColor: "text-red-600",
  },
  {
    icon: TrendingUp,
    title: "Investment",
    description: "Smart real estate investment opportunities",
    color: "from-yellow-500 to-yellow-600",
    bgColor: "bg-yellow-50",
    iconColor: "text-yellow-600",
  },
]

const features = [
  {
    icon: Users,
    value: "500+",
    label: "Happy Clients",
    description: "Satisfied customers across Ahmedabad",
  },
  {
    icon: Building2,
    value: "50+",
    label: "Premium Projects",
    description: "Luxury developments completed",
  },
  {
    icon: Award,
    value: "15+",
    label: "Years Experience",
    description: "Trusted expertise in real estate",
  },
]

export default function Hero() {
  const heroRef = useRef<HTMLElement>(null)
  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 })
  const [activeService, setActiveService] = useState<number | null>(null)
  const [currentWordIndex, setCurrentWordIndex] = useState(0)

  const animatedWords = ["Property", "Investment", "Real Estate", "Dream Home"]

  useEffect(() => {
    const handleMouseMove = (e: MouseEvent) => {
      setMousePosition({
        x: (e.clientX / window.innerWidth) * 100,
        y: (e.clientY / window.innerHeight) * 100,
      })
    }

    window.addEventListener("mousemove", handleMouseMove)
    return () => window.removeEventListener("mousemove", handleMouseMove)
  }, [])

  // Animated words rotation effect with proper exit/enter animation
  useEffect(() => {
    const interval = setInterval(() => {
      // Add exit class to current word
      const currentWord = document.querySelector('.rotate-word.active')
      if (currentWord) {
        currentWord.classList.add('exit')
        currentWord.classList.remove('active')
      }

      // After exit animation, change word and add active class
      setTimeout(() => {
        setCurrentWordIndex((prevIndex) => (prevIndex + 1) % animatedWords.length)

        // Remove exit class from all words
        document.querySelectorAll('.rotate-word').forEach(word => {
          word.classList.remove('exit')
        })
      }, 300) // Wait for exit animation
    }, 2500) // Change word every 2.5 seconds

    return () => clearInterval(interval)
  }, [animatedWords.length])

  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            entry.target.classList.add("animate-fade-in-up")
          }
        })
      },
      { threshold: 0.1 },
    )

    const elements = heroRef.current?.querySelectorAll(".animate-on-scroll")
    elements?.forEach((el) => observer.observe(el))

    return () => observer.disconnect()
  }, [])

  return (
    <section ref={heroRef} className="relative pt-24 pb-16 lg:pt-28 lg:pb-20 bg-gradient-to-r from-burgundy-600 to-burgundy-700 text-white overflow-hidden">
      {/* Background Image */}
      <div className="absolute inset-0 bg-[url('/images/hero-bg.jpg')] bg-cover bg-center opacity-20"></div>
      <div className="absolute inset-0 bg-gradient-to-r from-black/30 to-black/20"></div>

      {/* Floating Light Shapes */}
      <div className="absolute inset-0 pointer-events-none">
        <div
          className="absolute w-48 h-48 rounded-full bg-white/10 opacity-30 animate-float-minimal"
          style={{
            top: "10%",
            left: "5%",
            transform: `translate(${mousePosition.x * 0.05}px, ${mousePosition.y * 0.05}px)`,
          }}
        ></div>
        <div
          className="absolute w-32 h-32 rounded-xl bg-white/10 opacity-40 animate-float-minimal-reverse"
          style={{
            bottom: "15%",
            right: "10%",
            transform: `translate(${mousePosition.x * -0.03}px, ${mousePosition.y * -0.03}px)`,
          }}
        ></div>
        <div
          className="absolute w-24 h-24 rounded-full bg-white/10 opacity-20 animate-float-minimal"
          style={{
            top: "40%",
            right: "20%",
            transform: `translate(${mousePosition.x * 0.02}px, ${mousePosition.y * 0.02}px)`,
          }}
        ></div>
        <div
          className="absolute w-40 h-40 rounded-full bg-white/10 opacity-35 animate-float-minimal-reverse"
          style={{
            bottom: "5%",
            left: "25%",
            transform: `translate(${mousePosition.x * -0.04}px, ${mousePosition.y * -0.04}px)`,
          }}
        ></div>
      </div>

      {/* Main Hero Content */}
      <div className="container mx-auto px-4 relative z-10">
        <div className="pb-20">
          {/* Hero Header */}
          <div className="text-center mb-20">
            {/* Badge */}
            <div className="animate-on-scroll opacity-0 translate-y-8 mb-8">
              <Badge className="bg-white/20 text-white border-white/30 mb-6 px-4 py-2 text-sm font-medium">
                Premium Real Estate Services
              </Badge>
            </div>

            {/* Main Headline with Animated Text */}
            <div className="animate-on-scroll opacity-0 translate-y-8 delay-200 mb-8 text-center">
              <h1 className="text-5xl md:text-6xl md:text-8xl font-bold leading-tight text-white mb-6">
                <div className="block mb-6">Your Trusted</div>
                <div className="block mb-6 relative flex justify-center" style={{ height: '1.1em', minHeight: '120px', overflow: 'visible' }}>
                  <div className="rotating-text-container relative flex items-center justify-center w-full" style={{ overflow: 'visible' }}>
                    {animatedWords.map((word, index) => (
                      <span
                        key={word}
                        className={`rotating-word absolute whitespace-nowrap transition-all duration-700 ease-in-out transform text-white ${
                          index === currentWordIndex
                            ? 'opacity-100 translate-y-0 scale-100'
                            : 'opacity-0 translate-y-4 scale-85'
                        }`}
                        style={{
                          fontSize: '1em',
                          fontWeight: '800',
                          textShadow: '0 0 15px rgba(255, 255, 255, 0.3), 0 2px 4px rgba(0, 0, 0, 0.3)',
                          filter: 'drop-shadow(0 0 8px rgba(255, 255, 255, 0.2))',
                          overflow: 'visible',
                          zIndex: 10,
                        }}
                      >
                        {word}
                      </span>
                    ))}
                  </div>
                </div>
                <div className="block">Partner</div>
              </h1>
            </div>

            {/* Subtitle */}
            <div className="animate-on-scroll opacity-0 translate-y-8 delay-600 mb-12">
              <p className="text-xl md:text-2xl text-white/90 leading-relaxed max-w-3xl mx-auto font-light">
                Experience luxury living redefined with our comprehensive real estate services. From finding your dream
                home to smart investments, we're your trusted partner in Ahmedabad.
              </p>
            </div>

            {/* Action Buttons */}
            <div className="animate-on-scroll opacity-0 translate-y-8 delay-800 mb-16">
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Button
                  size="lg"
                  className="bg-white text-burgundy-600 hover:bg-gray-100 px-8 py-4 rounded-full font-semibold shadow-lg hover:shadow-xl transition-all duration-300 group border-0 hover:scale-105 transform"
                  asChild
                >
                  <Link href="/projects">
                    <span>Explore Properties</span>
                    <ArrowRight className="ml-2 group-hover:translate-x-1 transition-transform" size={20} />
                  </Link>
                </Button>

                <Button
                  variant="outline"
                  size="lg"
                  className="border-2 border-white text-white hover:border-white hover:text-burgundy-600 hover:bg-white px-8 py-4 rounded-full font-semibold transition-all duration-300 group hover:scale-105 transform bg-transparent"
                  asChild
                >
                  <Link href="/contact">
                    <MessageCircle className="mr-2 group-hover:scale-110 transition-transform" size={20} />
                    <span>Get Consultation</span>
                  </Link>
                </Button>
              </div>
            </div>

            {/* Trust Indicators */}

            {/* Trust Indicators - Consistent Card Sizing */}
            <div className="animate-on-scroll opacity-0 translate-y-8 delay-1000">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6 max-w-5xl mx-auto">
                {features.map((feature, index) => {
                  const IconComponent = feature.icon
                  return (
                    <div key={feature.label} className="group">
                      <div className="bg-white rounded-2xl p-8 shadow-sm border border-gray-100 hover:shadow-xl hover:border-burgundy-200 transition-all duration-500 hover:-translate-y-2 h-48 flex flex-col justify-center items-center text-center">
                        <div className="w-16 h-16 bg-burgundy-100 rounded-2xl flex items-center justify-center mx-auto mb-4 group-hover:bg-burgundy-200 group-hover:scale-110 transition-all duration-300">
                          <IconComponent className="text-burgundy-600" size={28} />
                        </div>
                        <div className="text-4xl font-bold text-burgundy-600 mb-2 group-hover:scale-105 transition-transform duration-300">
                          {feature.value}
                        </div>
                        <div className="text-lg font-semibold text-gray-900 mb-2">{feature.label}</div>
                        <div className="text-sm text-gray-600 leading-relaxed">{feature.description}</div>
                      </div>
                    </div>
                  )
                })}
              </div>
            </div>
          </div>


        </div>
      </div>

      {/* Scroll Indicator */}
    </section>
  )
}
